/**
 * Storage - 本地存储工具
 * 提供统一的本地存储接口，支持localStorage和sessionStorage
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * 本地存储工具类
 * 封装localStorage操作，提供错误处理和数据验证
 */
class Storage {
    constructor() {
        this.prefix = 'study_tracker_';
        this.isAvailable = this.checkAvailability();
    }

    /**
     * 检查localStorage是否可用
     * @returns {boolean} 是否可用
     */
    checkAvailability() {
        try {
            const testKey = '__storage_test__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            console.warn('localStorage不可用:', error);
            return false;
        }
    }

    /**
     * 生成带前缀的键名
     * @param {string} key - 原始键名
     * @returns {string} 带前缀的键名
     */
    getKey(key) {
        return this.prefix + key;
    }

    /**
     * 存储数据
     * @param {string} key - 键名
     * @param {*} value - 值
     * @returns {boolean} 是否成功
     */
    set(key, value) {
        if (!this.isAvailable) {
            console.warn('localStorage不可用，无法存储数据');
            return false;
        }

        try {
            const serializedValue = JSON.stringify(value);
            localStorage.setItem(this.getKey(key), serializedValue);
            return true;
        } catch (error) {
            console.error('数据存储失败:', error);
            return false;
        }
    }

    /**
     * 获取数据
     * @param {string} key - 键名
     * @param {*} defaultValue - 默认值
     * @returns {*} 存储的值或默认值
     */
    get(key, defaultValue = null) {
        if (!this.isAvailable) {
            return defaultValue;
        }

        try {
            const item = localStorage.getItem(this.getKey(key));
            if (item === null) {
                return defaultValue;
            }
            return JSON.parse(item);
        } catch (error) {
            console.error('数据读取失败:', error);
            return defaultValue;
        }
    }

    /**
     * 删除数据
     * @param {string} key - 键名
     * @returns {boolean} 是否成功
     */
    remove(key) {
        if (!this.isAvailable) {
            return false;
        }

        try {
            localStorage.removeItem(this.getKey(key));
            return true;
        } catch (error) {
            console.error('数据删除失败:', error);
            return false;
        }
    }

    /**
     * 清空所有应用数据
     * @returns {boolean} 是否成功
     */
    clear() {
        if (!this.isAvailable) {
            return false;
        }

        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (error) {
            console.error('数据清空失败:', error);
            return false;
        }
    }

    /**
     * 检查键是否存在
     * @param {string} key - 键名
     * @returns {boolean} 是否存在
     */
    has(key) {
        if (!this.isAvailable) {
            return false;
        }

        return localStorage.getItem(this.getKey(key)) !== null;
    }

    /**
     * 获取所有应用相关的键
     * @returns {Array} 键名数组
     */
    keys() {
        if (!this.isAvailable) {
            return [];
        }

        const keys = Object.keys(localStorage);
        return keys
            .filter(key => key.startsWith(this.prefix))
            .map(key => key.substring(this.prefix.length));
    }

    /**
     * 获取存储使用情况
     * @returns {Object} 使用情况统计
     */
    getUsage() {
        if (!this.isAvailable) {
            return { available: false };
        }

        try {
            let totalSize = 0;
            let appSize = 0;
            let appKeys = 0;

            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    const value = localStorage[key];
                    const size = new Blob([value]).size;
                    totalSize += size;

                    if (key.startsWith(this.prefix)) {
                        appSize += size;
                        appKeys++;
                    }
                }
            }

            return {
                available: true,
                totalSize: totalSize,
                appSize: appSize,
                appKeys: appKeys,
                totalSizeFormatted: this.formatBytes(totalSize),
                appSizeFormatted: this.formatBytes(appSize)
            };
        } catch (error) {
            console.error('获取存储使用情况失败:', error);
            return { available: false, error: error.message };
        }
    }

    /**
     * 格式化字节数
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';

        const units = ['B', 'KB', 'MB', 'GB'];
        const k = 1024;
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + units[i];
    }

    /**
     * 导出所有应用数据
     * @returns {Object} 导出的数据
     */
    export() {
        const data = {};
        const keys = this.keys();

        keys.forEach(key => {
            data[key] = this.get(key);
        });

        return {
            exportDate: new Date().toISOString(),
            version: '1.0.0',
            data: data
        };
    }

    /**
     * 导入数据
     * @param {Object} exportData - 导入的数据
     * @returns {boolean} 是否成功
     */
    import(exportData) {
        if (!exportData || !exportData.data) {
            console.error('无效的导入数据');
            return false;
        }

        try {
            Object.entries(exportData.data).forEach(([key, value]) => {
                this.set(key, value);
            });
            console.log('数据导入成功');
            return true;
        } catch (error) {
            console.error('数据导入失败:', error);
            return false;
        }
    }

    /**
     * 备份数据到文件
     * @param {string} filename - 文件名
     */
    backup(filename = null) {
        const data = this.export();
        const defaultFilename = `study_tracker_backup_${new Date().toISOString().split('T')[0]}.json`;
        const finalFilename = filename || defaultFilename;

        try {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = finalFilename;
            link.click();
            URL.revokeObjectURL(url);

            console.log('数据备份已下载:', finalFilename);
        } catch (error) {
            console.error('数据备份失败:', error);
        }
    }
}

// 创建单例实例
export const storage = new Storage();
