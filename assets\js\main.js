/**
 * 学习路线可视化系统 - 主入口文件
 * 负责初始化应用程序和协调各个模块
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { StudyTracker } from './core/StudyTracker.js';

/**
 * 应用程序主类
 * 负责应用程序的初始化和生命周期管理
 */
class App {
    constructor() {
        this.studyTracker = null;
        this.isInitialized = false;
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            console.log('正在初始化学习路线可视化系统...');
            
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 初始化StudyTracker
            this.studyTracker = new StudyTracker();
            await this.studyTracker.init();

            this.isInitialized = true;
            console.log('学习路线可视化系统初始化完成');
            
        } catch (error) {
            console.error('应用程序初始化失败:', error);
            this.handleInitError(error);
        }
    }

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    handleInitError(error) {
        const errorMessage = `
            <div style="
                position: fixed; 
                top: 50%; 
                left: 50%; 
                transform: translate(-50%, -50%);
                background: #fee; 
                border: 1px solid #fcc; 
                padding: 20px; 
                border-radius: 8px;
                max-width: 400px;
                text-align: center;
                z-index: 9999;
            ">
                <h3 style="color: #c33; margin: 0 0 10px 0;">系统初始化失败</h3>
                <p style="margin: 0 0 15px 0;">请刷新页面重试，如问题持续存在请联系技术支持。</p>
                <button onclick="location.reload()" style="
                    background: #c33; 
                    color: white; 
                    border: none; 
                    padding: 8px 16px; 
                    border-radius: 4px; 
                    cursor: pointer;
                ">刷新页面</button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', errorMessage);
    }

    /**
     * 获取StudyTracker实例
     * @returns {StudyTracker|null}
     */
    getStudyTracker() {
        return this.studyTracker;
    }

    /**
     * 检查应用程序是否已初始化
     * @returns {boolean}
     */
    isReady() {
        return this.isInitialized;
    }
}

// 创建全局应用实例
const app = new App();

// 启动应用程序
app.init().catch(error => {
    console.error('应用程序启动失败:', error);
});

// 导出应用实例供调试使用
window.app = app;
