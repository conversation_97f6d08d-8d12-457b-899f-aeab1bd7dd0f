/**
 * StudyContent - 学习内容组件
 * 负责学习内容的渲染和交互
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { getFileIcon } from '../utils/helpers.js';

/**
 * 学习内容组件类
 */
export class StudyContent {
    constructor() {
        this.eventListeners = new Map();
        this.container = null;
        this.studyData = [];
        this.progress = {};
    }

    /**
     * 初始化组件
     */
    async init() {
        try {
            console.log('正在初始化学习内容组件...');

            // 获取容器元素
            this.container = document.getElementById('studyContent');
            if (!this.container) {
                throw new Error('未找到学习内容容器');
            }

            // 从本地存储加载进度
            this.loadProgress();

            console.log('学习内容组件初始化完成');

        } catch (error) {
            console.error('学习内容组件初始化失败:', error);
            throw error;
        }
    }

    /**
     * 从本地存储加载进度
     */
    loadProgress() {
        try {
            const savedProgress = localStorage.getItem('studyProgress');
            this.progress = savedProgress ? JSON.parse(savedProgress) : {};
        } catch (error) {
            console.warn('进度数据加载失败:', error);
            this.progress = {};
        }
    }

    /**
     * 渲染学习内容
     * @param {Array} studyData - 学习数据
     */
    async render(studyData) {
        try {
            console.log('🎨 渲染学习内容');
            this.studyData = studyData;

            if (!studyData || studyData.length === 0) {
                this.renderEmptyState();
                return;
            }

            let html = '';
            studyData.forEach(module => {
                html += this.renderModule(module);
            });

            this.container.innerHTML = html;
            this.bindEvents();

        } catch (error) {
            console.error('学习内容渲染失败:', error);
            this.renderErrorState(error.message);
        }
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        this.container.innerHTML = `
            <div class="card p-8 rounded-lg text-center">
                <i class="fas fa-exclamation-triangle text-6xl mb-4 text-yellow-500"></i>
                <h3 class="text-xl font-semibold mb-2">未找到学习内容</h3>
                <p style="color: var(--text-secondary);">请检查Markdown文件格式是否正确</p>
            </div>
        `;
    }

    /**
     * 渲染错误状态
     * @param {string} errorMessage - 错误消息
     */
    renderErrorState(errorMessage) {
        this.container.innerHTML = `
            <div class="card p-8 rounded-lg text-center">
                <i class="fas fa-exclamation-circle text-6xl mb-4 text-red-500"></i>
                <h3 class="text-xl font-semibold mb-2">渲染失败</h3>
                <p style="color: var(--text-secondary);">${errorMessage}</p>
            </div>
        `;
    }

    /**
     * 渲染模块
     * @param {Object} module - 模块数据
     * @returns {string} HTML字符串
     */
    renderModule(module) {
        const isExpanded = module.expanded;
        const totalDocs = this.getTotalDocuments(module);
        const completedDocs = this.getCompletedDocuments(module);
        const progressPercent = totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0;

        let html = `
            <div class="card rounded-lg mb-4 fade-in">
                <div class="module-header p-4 flex items-center justify-between" data-module-id="${module.id}">
                    <div class="flex items-center">
                        <i class="fas fa-chevron-${isExpanded ? 'down' : 'right'} mr-3 transition-transform"></i>
                        <i class="fas fa-book mr-3" style="color: var(--accent-color);"></i>
                        <h3 class="text-lg font-semibold">${module.title}</h3>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm px-3 py-1 bg-blue-100 text-blue-800 rounded-full">
                            ${completedDocs}/${totalDocs} (${progressPercent}%)
                        </span>
                        <div class="w-20 bg-gray-200 rounded-full h-2">
                            <div class="progress-bar h-2 rounded-full" style="width: ${progressPercent}%"></div>
                        </div>
                    </div>
                </div>
                <div class="module-content ${isExpanded ? '' : 'hidden'}" id="content-${module.id}">
        `;

        module.resources.forEach(resource => {
            html += this.renderResource(resource);
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    /**
     * 渲染资源
     * @param {Object} resource - 资源数据
     * @returns {string} HTML字符串
     */
    renderResource(resource) {
        const isExpanded = resource.expanded;
        const totalDocs = resource.documents.length;
        const completedDocs = resource.documents.filter(doc => this.progress[doc.id]).length;
        const progressPercent = totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0;

        let html = `
            <div class="border-l-4 border-blue-300 ml-8 mb-3">
                <div class="module-header p-3 ml-4 flex items-center justify-between" data-resource-id="${resource.id}">
                    <div class="flex items-center">
                        <i class="fas fa-chevron-${isExpanded ? 'down' : 'right'} mr-3 transition-transform text-sm"></i>
                        <i class="fas fa-folder mr-3 text-yellow-500"></i>
                        <h4 class="font-medium">${resource.title}</h4>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                            ${completedDocs}/${totalDocs}
                        </span>
                        <div class="w-16 bg-gray-200 rounded-full h-1">
                            <div class="progress-bar h-1 rounded-full" style="width: ${progressPercent}%"></div>
                        </div>
                    </div>
                </div>
                <div class="resource-content ${isExpanded ? '' : 'hidden'} ml-4" id="content-${resource.id}">
        `;

        resource.documents.forEach(doc => {
            const isCompleted = this.progress[doc.id] || false;
            html += `
                <div class="flex items-center p-2 ml-8 hover:bg-gray-50 rounded transition-colors">
                    <input type="checkbox"
                           class="checkbox-custom mr-3"
                           data-doc-id="${doc.id}"
                           ${isCompleted ? 'checked' : ''}>
                    <i class="${getFileIcon(doc.title)} mr-2"></i>
                    <span class="${isCompleted ? 'line-through opacity-60' : ''}">${doc.title}</span>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 模块折叠/展开事件
        this.container.querySelectorAll('[data-module-id]').forEach(header => {
            header.addEventListener('click', () => {
                const moduleId = header.dataset.moduleId;
                this.toggleModule(moduleId);
            });
        });

        // 资源折叠/展开事件
        this.container.querySelectorAll('[data-resource-id]').forEach(header => {
            header.addEventListener('click', () => {
                const resourceId = header.dataset.resourceId;
                this.toggleResource(resourceId);
            });
        });

        // 复选框事件
        this.container.querySelectorAll('[data-doc-id]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const docId = e.target.dataset.docId;
                this.toggleDocumentCompletion(docId, e.target.checked);
            });
        });
    }

    /**
     * 切换模块展开/折叠状态
     * @param {string} moduleId - 模块ID
     */
    toggleModule(moduleId) {
        const module = this.studyData.find(m => m.id === moduleId);
        if (module) {
            module.expanded = !module.expanded;
            this.render(this.studyData);
        }
    }

    /**
     * 切换资源展开/折叠状态
     * @param {string} resourceId - 资源ID
     */
    toggleResource(resourceId) {
        this.studyData.forEach(module => {
            const resource = module.resources.find(r => r.id === resourceId);
            if (resource) {
                resource.expanded = !resource.expanded;
            }
        });
        this.render(this.studyData);
    }

    /**
     * 切换文档完成状态
     * @param {string} docId - 文档ID
     * @param {boolean} completed - 是否完成
     */
    toggleDocumentCompletion(docId, completed) {
        this.progress[docId] = completed;
        localStorage.setItem('studyProgress', JSON.stringify(this.progress));
        this.emit('progressChanged', {
            docId,
            completed,
            progress: this.progress
        });
        this.render(this.studyData);
    }

    /**
     * 获取模块总文档数
     * @param {Object} module - 模块数据
     * @returns {number} 总文档数
     */
    getTotalDocuments(module) {
        return module.resources.reduce((total, resource) => total + resource.documents.length, 0);
    }

    /**
     * 获取模块已完成文档数
     * @param {Object} module - 模块数据
     * @returns {number} 已完成文档数
     */
    getCompletedDocuments(module) {
        return module.resources.reduce((total, resource) => {
            return total + resource.documents.filter(doc => this.progress[doc.id]).length;
        }, 0);
    }

    /**
     * 更新进度显示
     * @param {Object} progressData - 进度数据
     */
    updateProgress(progressData) {
        this.progress = progressData.progress || this.progress;
        if (this.studyData.length > 0) {
            this.render(this.studyData);
        }
    }

    /**
     * 展开所有模块
     */
    expandAll() {
        this.studyData.forEach(module => {
            module.expanded = true;
            module.resources.forEach(resource => {
                resource.expanded = true;
            });
        });
        this.render(this.studyData);
    }

    /**
     * 折叠所有模块
     */
    collapseAll() {
        this.studyData.forEach(module => {
            module.expanded = false;
            module.resources.forEach(resource => {
                resource.expanded = false;
            });
        });
        this.render(this.studyData);
    }

    /**
     * 更新主题
     * @param {string} theme - 主题名称
     */
    updateTheme(theme) {
        console.log('学习内容主题更新:', theme);
        // 主题更新逻辑可以在这里添加
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (!this.eventListeners.has(event)) return;

        const listeners = this.eventListeners.get(event);
        listeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('学习内容事件处理错误:', error);
            }
        });
    }
}
