<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习路线可视化系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        :root {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --border-color: #e2e8f0;
            --accent-color: #3182ce;
            --success-color: #38a169;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --bg-primary: #1a202c;
            --bg-secondary: #2d3748;
            --text-primary: #f7fafc;
            --text-secondary: #cbd5e0;
            --border-color: #4a5568;
            --accent-color: #63b3ed;
            --success-color: #68d391;
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .card {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--success-color) 0%, var(--accent-color) 100%);
            transition: width 0.3s ease;
        }

        .file-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }

        .module-header {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .module-header:hover {
            background-color: var(--border-color);
        }

        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .checkbox-custom {
            appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 3px;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .checkbox-custom:checked {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .checkbox-custom:checked::after {
            content: '✓';
            position: absolute;
            color: white;
            font-size: 12px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .upload-zone {
            border: 2px dashed var(--border-color);
            transition: all 0.3s ease;
        }

        .upload-zone:hover {
            border-color: var(--accent-color);
            background-color: var(--bg-secondary);
        }

        .time-display {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="min-h-screen p-4">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <header class="mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-3xl font-bold text-center flex items-center">
                        <i class="fas fa-route mr-3" style="color: var(--accent-color);"></i>
                        学习路线可视化系统
                    </h1>
                    <button id="themeToggle" class="p-2 rounded-lg card hover:shadow-lg transition-all">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
                
                <!-- Stats Dashboard -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="card p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-blue-500 mr-3"></i>
                            <div>
                                <p class="text-sm" style="color: var(--text-secondary);">今日学习</p>
                                <p class="time-display" id="todayTime">0 分钟</p>
                            </div>
                        </div>
                    </div>
                    <div class="card p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-alt text-green-500 mr-3"></i>
                            <div>
                                <p class="text-sm" style="color: var(--text-secondary);">累计学习</p>
                                <p class="time-display" id="totalTime">0 分钟</p>
                            </div>
                        </div>
                    </div>
                    <div class="card p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-tasks text-purple-500 mr-3"></i>
                            <div>
                                <p class="text-sm" style="color: var(--text-secondary);">学习进度</p>
                                <p class="time-display" id="progressText">0/0 (0%)</p>
                            </div>
                        </div>
                    </div>
                    <div class="card p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-fire text-red-500 mr-3"></i>
                            <div>
                                <p class="text-sm" style="color: var(--text-secondary);">学习状态</p>
                                <p class="time-display" id="studyStatus">未开始</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="card p-4 rounded-lg mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium">整体进度</span>
                        <span class="text-sm" id="progressPercentage">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="progress-bar h-3 rounded-full" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
            </header>

            <!-- File Upload Section -->
            <section class="mb-8">
                <div class="card p-6 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4 flex items-center">
                        <i class="fas fa-upload mr-2"></i>
                        上传学习计划文件
                    </h2>
                    <div class="upload-zone p-8 rounded-lg text-center cursor-pointer" id="uploadZone">
                        <i class="fas fa-cloud-upload-alt text-4xl mb-4" style="color: var(--accent-color);"></i>
                        <p class="text-lg mb-2">拖拽Markdown文件到此处或点击选择</p>
                        <p class="text-sm" style="color: var(--text-secondary);">支持 .md 格式文件</p>
                        <input type="file" id="fileInput" class="hidden" accept=".md">
                    </div>
                </div>
            </section>

            <!-- Control Buttons -->
            <section class="mb-6">
                <div class="flex flex-wrap gap-3">
                    <button id="expandAll" class="px-4 py-2 card rounded-lg hover:shadow-lg transition-all">
                        <i class="fas fa-expand-arrows-alt mr-2"></i>全部展开
                    </button>
                    <button id="collapseAll" class="px-4 py-2 card rounded-lg hover:shadow-lg transition-all">
                        <i class="fas fa-compress-arrows-alt mr-2"></i>全部折叠
                    </button>
                    <button id="resetProgress" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all">
                        <i class="fas fa-undo mr-2"></i>重置进度
                    </button>
                    <button id="exportProgress" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-all">
                        <i class="fas fa-download mr-2"></i>导出进度
                    </button>
                </div>
            </section>

            <!-- Study Content -->
            <main id="studyContent" class="mb-8">
                <div class="card p-8 rounded-lg text-center">
                    <i class="fas fa-book-open text-6xl mb-4" style="color: var(--accent-color);"></i>
                    <h3 class="text-xl font-semibold mb-2">准备开始学习</h3>
                    <p style="color: var(--text-secondary);">请先上传您的学习计划Markdown文件</p>
                </div>
            </main>
        </div>
    </div>

    <script>
        class StudyTracker {
            constructor() {
                this.studyData = [];
                this.progress = JSON.parse(localStorage.getItem('studyProgress') || '{}');
                this.theme = localStorage.getItem('theme') || 'light';
                this.timeData = JSON.parse(localStorage.getItem('timeData') || '{"today": 0, "total": 0, "lastDate": ""}');
                this.isStudying = false;
                this.startTime = null;
                this.timeInterval = null;
                
                this.initializeTheme();
                this.bindEvents();
                this.startTimeTracking();
                this.updateTimeDisplay();
                this.checkNewDay();
            }

            checkNewDay() {
                const today = new Date().toDateString();
                if (this.timeData.lastDate !== today) {
                    this.timeData.today = 0;
                    this.timeData.lastDate = today;
                    this.saveTimeData();
                }
            }

            initializeTheme() {
                document.documentElement.setAttribute('data-theme', this.theme);
                const themeIcon = document.querySelector('#themeToggle i');
                themeIcon.className = this.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            bindEvents() {
                // Theme toggle
                document.getElementById('themeToggle').addEventListener('click', () => {
                    this.toggleTheme();
                });

                // File upload
                const uploadZone = document.getElementById('uploadZone');
                const fileInput = document.getElementById('fileInput');

                uploadZone.addEventListener('click', () => fileInput.click());
                uploadZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = 'var(--accent-color)';
                });
                uploadZone.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    uploadZone.style.borderColor = 'var(--border-color)';
                });
                uploadZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.handleFileUpload(files[0]);
                    }
                    uploadZone.style.borderColor = 'var(--border-color)';
                });

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.handleFileUpload(e.target.files[0]);
                    }
                });

                // Control buttons
                document.getElementById('expandAll').addEventListener('click', () => {
                    this.expandAllModules();
                });
                document.getElementById('collapseAll').addEventListener('click', () => {
                    this.collapseAllModules();
                });
                document.getElementById('resetProgress').addEventListener('click', () => {
                    this.resetProgress();
                });
                document.getElementById('exportProgress').addEventListener('click', () => {
                    this.exportProgress();
                });

                // Page visibility change for time tracking
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.pauseStudying();
                    } else {
                        this.resumeStudying();
                    }
                });
            }

            toggleTheme() {
                this.theme = this.theme === 'light' ? 'dark' : 'light';
                localStorage.setItem('theme', this.theme);
                this.initializeTheme();
            }

            async handleFileUpload(file) {
                if (!file.name.endsWith('.md')) {
                    alert('请上传Markdown文件（.md格式）');
                    return;
                }

                try {
                    const content = await this.readFile(file);
                    this.parseMarkdown(content);
                    this.renderStudyContent();
                    this.updateProgress();
                    this.startStudying();
                } catch (error) {
                    alert('文件读取失败：' + error.message);
                }
            }

            readFile(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = e => resolve(e.target.result);
                    reader.onerror = reject;
                    reader.readAsText(file);
                });
            }

            parseMarkdown(content) {
                const lines = content.split('\n');
                this.studyData = [];
                let currentModule = null;
                let currentResource = null;

                lines.forEach(line => {
                    line = line.trim();
                    if (line.startsWith('## ')) {
                        currentModule = {
                            id: 'module_' + Date.now() + '_' + Math.random(),
                            title: line.substring(3).trim(),
                            type: 'module',
                            resources: [],
                            expanded: false
                        };
                        this.studyData.push(currentModule);
                        currentResource = null;
                    } else if (line.startsWith('### ')) {
                        if (currentModule) {
                            currentResource = {
                                id: 'resource_' + Date.now() + '_' + Math.random(),
                                title: line.substring(4).trim(),
                                type: 'resource',
                                documents: [],
                                expanded: false
                            };
                            currentModule.resources.push(currentResource);
                        }
                    } else if (line.startsWith('- ')) {
                        if (currentResource) {
                            const document = {
                                id: 'doc_' + Date.now() + '_' + Math.random(),
                                title: line.substring(2).trim(),
                                type: 'document',
                                completed: false
                            };
                            currentResource.documents.push(document);
                        }
                    }
                });
            }

            getFileIcon(filename) {
                const ext = filename.split('.').pop()?.toLowerCase();
                const iconMap = {
                    'pdf': 'fas fa-file-pdf text-red-500',
                    'doc': 'fas fa-file-word text-blue-500',
                    'docx': 'fas fa-file-word text-blue-500',
                    'ppt': 'fas fa-file-powerpoint text-orange-500',
                    'pptx': 'fas fa-file-powerpoint text-orange-500',
                    'xls': 'fas fa-file-excel text-green-500',
                    'xlsx': 'fas fa-file-excel text-green-500',
                    'jpg': 'fas fa-file-image text-purple-500',
                    'jpeg': 'fas fa-file-image text-purple-500',
                    'png': 'fas fa-file-image text-purple-500',
                    'gif': 'fas fa-file-image text-purple-500',
                    'mp4': 'fas fa-file-video text-red-600',
                    'mp3': 'fas fa-file-audio text-green-600',
                    'wav': 'fas fa-file-audio text-green-600',
                    'zip': 'fas fa-file-archive text-yellow-500',
                    'rar': 'fas fa-file-archive text-yellow-500',
                    'txt': 'fas fa-file-alt text-gray-500',
                    'md': 'fas fa-file-alt text-blue-400'
                };
                return iconMap[ext] || 'fas fa-file text-gray-400';
            }

            renderStudyContent() {
                const container = document.getElementById('studyContent');
                if (this.studyData.length === 0) {
                    container.innerHTML = `
                        <div class="card p-8 rounded-lg text-center">
                            <i class="fas fa-exclamation-triangle text-6xl mb-4 text-yellow-500"></i>
                            <h3 class="text-xl font-semibold mb-2">未找到学习内容</h3>
                            <p style="color: var(--text-secondary);">请检查Markdown文件格式是否正确</p>
                        </div>
                    `;
                    return;
                }

                let html = '';
                this.studyData.forEach(module => {
                    html += this.renderModule(module);
                });

                container.innerHTML = html;
                this.bindModuleEvents();
            }

            renderModule(module) {
                const isExpanded = module.expanded;
                const totalDocs = this.getTotalDocuments(module);
                const completedDocs = this.getCompletedDocuments(module);
                const progressPercent = totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0;

                let html = `
                    <div class="card rounded-lg mb-4 fade-in">
                        <div class="module-header p-4 flex items-center justify-between" data-module-id="${module.id}">
                            <div class="flex items-center">
                                <i class="fas fa-chevron-${isExpanded ? 'down' : 'right'} mr-3 transition-transform"></i>
                                <i class="fas fa-book mr-3" style="color: var(--accent-color);"></i>
                                <h3 class="text-lg font-semibold">${module.title}</h3>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm px-3 py-1 bg-blue-100 text-blue-800 rounded-full">
                                    ${completedDocs}/${totalDocs} (${progressPercent}%)
                                </span>
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="progress-bar h-2 rounded-full" style="width: ${progressPercent}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="module-content ${isExpanded ? '' : 'hidden'}" id="content-${module.id}">
                `;

                module.resources.forEach(resource => {
                    html += this.renderResource(resource);
                });

                html += `
                        </div>
                    </div>
                `;

                return html;
            }

            renderResource(resource) {
                const isExpanded = resource.expanded;
                const totalDocs = resource.documents.length;
                const completedDocs = resource.documents.filter(doc => this.progress[doc.id]).length;
                const progressPercent = totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0;

                let html = `
                    <div class="border-l-4 border-blue-300 ml-8 mb-3">
                        <div class="module-header p-3 ml-4 flex items-center justify-between" data-resource-id="${resource.id}">
                            <div class="flex items-center">
                                <i class="fas fa-chevron-${isExpanded ? 'down' : 'right'} mr-3 transition-transform text-sm"></i>
                                <i class="fas fa-folder mr-3 text-yellow-500"></i>
                                <h4 class="font-medium">${resource.title}</h4>
                            </div>
                            <div class="flex items-center space-x-3">
                                <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                                    ${completedDocs}/${totalDocs}
                                </span>
                                <div class="w-16 bg-gray-200 rounded-full h-1">
                                    <div class="progress-bar h-1 rounded-full" style="width: ${progressPercent}%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="resource-content ${isExpanded ? '' : 'hidden'} ml-4" id="content-${resource.id}">
                `;

                resource.documents.forEach(doc => {
                    const isCompleted = this.progress[doc.id] || false;
                    html += `
                        <div class="flex items-center p-2 ml-8 hover:bg-gray-50 rounded transition-colors">
                            <input type="checkbox" 
                                   class="checkbox-custom mr-3" 
                                   data-doc-id="${doc.id}"
                                   ${isCompleted ? 'checked' : ''}>
                            <i class="${this.getFileIcon(doc.title)} mr-2"></i>
                            <span class="${isCompleted ? 'line-through opacity-60' : ''}">${doc.title}</span>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;

                return html;
            }

            bindModuleEvents() {
                // Module toggle events
                document.querySelectorAll('[data-module-id]').forEach(header => {
                    header.addEventListener('click', () => {
                        const moduleId = header.dataset.moduleId;
                        this.toggleModule(moduleId);
                    });
                });

                // Resource toggle events
                document.querySelectorAll('[data-resource-id]').forEach(header => {
                    header.addEventListener('click', () => {
                        const resourceId = header.dataset.resourceId;
                        this.toggleResource(resourceId);
                    });
                });

                // Checkbox events
                document.querySelectorAll('[data-doc-id]').forEach(checkbox => {
                    checkbox.addEventListener('change', (e) => {
                        const docId = e.target.dataset.docId;
                        this.toggleDocumentCompletion(docId, e.target.checked);
                    });
                });
            }

            toggleModule(moduleId) {
                const module = this.studyData.find(m => m.id === moduleId);
                if (module) {
                    module.expanded = !module.expanded;
                    this.renderStudyContent();
                }
            }

            toggleResource(resourceId) {
                this.studyData.forEach(module => {
                    const resource = module.resources.find(r => r.id === resourceId);
                    if (resource) {
                        resource.expanded = !resource.expanded;
                    }
                });
                this.renderStudyContent();
            }

            toggleDocumentCompletion(docId, completed) {
                this.progress[docId] = completed;
                localStorage.setItem('studyProgress', JSON.stringify(this.progress));
                this.updateProgress();
                this.renderStudyContent();
            }

            expandAllModules() {
                this.studyData.forEach(module => {
                    module.expanded = true;
                    module.resources.forEach(resource => {
                        resource.expanded = true;
                    });
                });
                this.renderStudyContent();
            }

            collapseAllModules() {
                this.studyData.forEach(module => {
                    module.expanded = false;
                    module.resources.forEach(resource => {
                        resource.expanded = false;
                    });
                });
                this.renderStudyContent();
            }

            resetProgress() {
                if (confirm('确定要重置所有学习进度吗？此操作不可恢复。')) {
                    this.progress = {};
                    localStorage.removeItem('studyProgress');
                    this.updateProgress();
                    this.renderStudyContent();
                }
            }

            exportProgress() {
                const totalDocs = this.getTotalDocuments();
                const completedDocs = this.getCompletedDocuments();
                
                const report = {
                    exportDate: new Date().toISOString(),
                    totalDocuments: totalDocs,
                    completedDocuments: completedDocs,
                    progressPercentage: totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0,
                    timeData: this.timeData,
                    detailedProgress: this.generateDetailedReport()
                };

                const blob = new Blob([JSON.stringify(report, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `study_progress_${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }

            generateDetailedReport() {
                return this.studyData.map(module => ({
                    module: module.title,
                    totalDocs: this.getTotalDocuments(module),
                    completedDocs: this.getCompletedDocuments(module),
                    resources: module.resources.map(resource => ({
                        resource: resource.title,
                        totalDocs: resource.documents.length,
                        completedDocs: resource.documents.filter(doc => this.progress[doc.id]).length,
                        documents: resource.documents.map(doc => ({
                            title: doc.title,
                            completed: !!this.progress[doc.id]
                        }))
                    }))
                }));
            }

            getTotalDocuments(module = null) {
                if (module) {
                    return module.resources.reduce((total, resource) => total + resource.documents.length, 0);
                }
                return this.studyData.reduce((total, module) => total + this.getTotalDocuments(module), 0);
            }

            getCompletedDocuments(module = null) {
                if (module) {
                    return module.resources.reduce((total, resource) => {
                        return total + resource.documents.filter(doc => this.progress[doc.id]).length;
                    }, 0);
                }
                return this.studyData.reduce((total, module) => total + this.getCompletedDocuments(module), 0);
            }

            updateProgress() {
                const totalDocs = this.getTotalDocuments();
                const completedDocs = this.getCompletedDocuments();
                const percentage = totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0;

                document.getElementById('progressText').textContent = `${completedDocs}/${totalDocs} (${percentage}%)`;
                document.getElementById('progressPercentage').textContent = `${percentage}%`;
                document.getElementById('progressBar').style.width = `${percentage}%`;
            }

            startTimeTracking() {
                this.startStudying();
                this.timeInterval = setInterval(() => {
                    if (this.isStudying) {
                        this.timeData.today += 1;
                        this.timeData.total += 1;
                        this.saveTimeData();
                        this.updateTimeDisplay();
                    }
                }, 60000); // Update every minute
            }

            startStudying() {
                if (!this.isStudying && !document.hidden) {
                    this.isStudying = true;
                    this.startTime = Date.now();
                    document.getElementById('studyStatus').textContent = '学习中';
                }
            }

            pauseStudying() {
                if (this.isStudying) {
                    this.isStudying = false;
                    document.getElementById('studyStatus').textContent = '已暂停';
                }
            }

            resumeStudying() {
                if (!this.isStudying && this.studyData.length > 0) {
                    this.startStudying();
                }
            }

            saveTimeData() {
                localStorage.setItem('timeData', JSON.stringify(this.timeData));
            }

            updateTimeDisplay() {
                document.getElementById('todayTime').textContent = `${this.timeData.today} 分钟`;
                document.getElementById('totalTime').textContent = `${this.timeData.total} 分钟`;
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new StudyTracker();
        });
    </script>
</body>
</html>
