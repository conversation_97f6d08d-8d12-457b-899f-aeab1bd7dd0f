<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习路线可视化系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <!-- 自定义样式文件 -->
    <link rel="stylesheet" href="assets/css/themes.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/animations.css">
</head>
<body>
    <div class="min-h-screen p-4">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <header class="mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-3xl font-bold text-center flex items-center">
                        <i class="fas fa-route mr-3" style="color: var(--accent-color);"></i>
                        学习路线可视化系统
                    </h1>
                    <button id="themeToggle" class="p-2 rounded-lg card hover:shadow-lg transition-all">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
                
                <!-- Stats Dashboard -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="card p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-blue-500 mr-3"></i>
                            <div>
                                <p class="text-sm" style="color: var(--text-secondary);">今日学习</p>
                                <p class="time-display" id="todayTime">0 分钟</p>
                            </div>
                        </div>
                    </div>
                    <div class="card p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-calendar-alt text-green-500 mr-3"></i>
                            <div>
                                <p class="text-sm" style="color: var(--text-secondary);">累计学习</p>
                                <p class="time-display" id="totalTime">0 分钟</p>
                            </div>
                        </div>
                    </div>
                    <div class="card p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-tasks text-purple-500 mr-3"></i>
                            <div>
                                <p class="text-sm" style="color: var(--text-secondary);">学习进度</p>
                                <p class="time-display" id="progressText">0/0 (0%)</p>
                            </div>
                        </div>
                    </div>
                    <div class="card p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-fire text-red-500 mr-3"></i>
                            <div>
                                <p class="text-sm" style="color: var(--text-secondary);">学习状态</p>
                                <p class="time-display" id="studyStatus">未开始</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="card p-4 rounded-lg mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium">整体进度</span>
                        <span class="text-sm" id="progressPercentage">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="progress-bar h-3 rounded-full" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
            </header>

            <!-- File Upload Section -->
            <section class="mb-8">
                <div class="card p-6 rounded-lg">
                    <h2 class="text-xl font-semibold mb-4 flex items-center">
                        <i class="fas fa-upload mr-2"></i>
                        上传学习计划文件
                    </h2>
                    <div class="upload-zone p-8 rounded-lg text-center cursor-pointer" id="uploadZone">
                        <i class="fas fa-cloud-upload-alt text-4xl mb-4" style="color: var(--accent-color);"></i>
                        <p class="text-lg mb-2">拖拽Markdown文件到此处或点击选择</p>
                        <p class="text-sm" style="color: var(--text-secondary);">支持 .md 格式文件</p>
                        <input type="file" id="fileInput" class="hidden" accept=".md">
                    </div>
                </div>
            </section>

            <!-- Control Buttons -->
            <section class="mb-6">
                <div class="flex flex-wrap gap-3">
                    <button id="expandAll" class="px-4 py-2 card rounded-lg hover:shadow-lg transition-all">
                        <i class="fas fa-expand-arrows-alt mr-2"></i>全部展开
                    </button>
                    <button id="collapseAll" class="px-4 py-2 card rounded-lg hover:shadow-lg transition-all">
                        <i class="fas fa-compress-arrows-alt mr-2"></i>全部折叠
                    </button>
                    <button id="resetProgress" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all">
                        <i class="fas fa-undo mr-2"></i>重置进度
                    </button>
                    <button id="exportProgress" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-all">
                        <i class="fas fa-download mr-2"></i>导出进度
                    </button>
                </div>
            </section>

            <!-- Study Content -->
            <main id="studyContent" class="mb-8">
                <div class="card p-8 rounded-lg text-center">
                    <i class="fas fa-book-open text-6xl mb-4" style="color: var(--accent-color);"></i>
                    <h3 class="text-xl font-semibold mb-2">准备开始学习</h3>
                    <p style="color: var(--text-secondary);">请先上传您的学习计划Markdown文件</p>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript模块 -->
    <script type="module" src="assets/js/main.js"></script>



            toggleDocumentCompletion(docId, completed) {
                this.progress[docId] = completed;
                localStorage.setItem('studyProgress', JSON.stringify(this.progress));
                this.updateProgress();
                this.renderStudyContent();
            }

            expandAllModules() {
                this.studyData.forEach(module => {
                    module.expanded = true;
                    module.resources.forEach(resource => {
                        resource.expanded = true;
                    });
                });
                this.renderStudyContent();
            }

            collapseAllModules() {
                this.studyData.forEach(module => {
                    module.expanded = false;
                    module.resources.forEach(resource => {
                        resource.expanded = false;
                    });
                });
                this.renderStudyContent();
            }

            resetProgress() {
                if (confirm('确定要重置所有学习进度吗？此操作不可恢复。')) {
                    this.progress = {};
                    localStorage.removeItem('studyProgress');
                    this.updateProgress();
                    this.renderStudyContent();
                }
            }

            exportProgress() {
                const totalDocs = this.getTotalDocuments();
                const completedDocs = this.getCompletedDocuments();
                
                const report = {
                    exportDate: new Date().toISOString(),
                    totalDocuments: totalDocs,
                    completedDocuments: completedDocs,
                    progressPercentage: totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0,
                    timeData: this.timeData,
                    detailedProgress: this.generateDetailedReport()
                };

                const blob = new Blob([JSON.stringify(report, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `study_progress_${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }

            generateDetailedReport() {
                return this.studyData.map(module => ({
                    module: module.title,
                    totalDocs: this.getTotalDocuments(module),
                    completedDocs: this.getCompletedDocuments(module),
                    resources: module.resources.map(resource => ({
                        resource: resource.title,
                        totalDocs: resource.documents.length,
                        completedDocs: resource.documents.filter(doc => this.progress[doc.id]).length,
                        documents: resource.documents.map(doc => ({
                            title: doc.title,
                            completed: !!this.progress[doc.id]
                        }))
                    }))
                }));
            }

            getTotalDocuments(module = null) {
                if (module) {
                    return module.resources.reduce((total, resource) => total + resource.documents.length, 0);
                }
                return this.studyData.reduce((total, module) => total + this.getTotalDocuments(module), 0);
            }

            getCompletedDocuments(module = null) {
                if (module) {
                    return module.resources.reduce((total, resource) => {
                        return total + resource.documents.filter(doc => this.progress[doc.id]).length;
                    }, 0);
                }
                return this.studyData.reduce((total, module) => total + this.getCompletedDocuments(module), 0);
            }

            updateProgress() {
                const totalDocs = this.getTotalDocuments();
                const completedDocs = this.getCompletedDocuments();
                const percentage = totalDocs > 0 ? Math.round((completedDocs / totalDocs) * 100) : 0;

                document.getElementById('progressText').textContent = `${completedDocs}/${totalDocs} (${percentage}%)`;
                document.getElementById('progressPercentage').textContent = `${percentage}%`;
                document.getElementById('progressBar').style.width = `${percentage}%`;
            }

            startTimeTracking() {
                this.startStudying();
                this.timeInterval = setInterval(() => {
                    if (this.isStudying) {
                        this.timeData.today += 1;
                        this.timeData.total += 1;
                        this.saveTimeData();
                        this.updateTimeDisplay();
                    }
                }, 60000); // Update every minute
            }

            startStudying() {
                if (!this.isStudying && !document.hidden) {
                    this.isStudying = true;
                    this.startTime = Date.now();
                    document.getElementById('studyStatus').textContent = '学习中';
                }
            }

            pauseStudying() {
                if (this.isStudying) {
                    this.isStudying = false;
                    document.getElementById('studyStatus').textContent = '已暂停';
                }
            }

            resumeStudying() {
                if (!this.isStudying && this.studyData.length > 0) {
                    this.startStudying();
                }
            }

            saveTimeData() {
                localStorage.setItem('timeData', JSON.stringify(this.timeData));
            }

            updateTimeDisplay() {
                document.getElementById('todayTime').textContent = `${this.timeData.today} 分钟`;
                document.getElementById('totalTime').textContent = `${this.timeData.total} 分钟`;
            }
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            new StudyTracker();
        });
    </script>
</body>
</html>
