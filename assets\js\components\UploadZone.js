/**
 * UploadZone - 上传区域组件
 * 负责文件上传功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * 上传区域组件类
 */
export class UploadZone {
    constructor() {
        this.eventListeners = new Map();
        this.uploadZone = null;
        this.fileInput = null;
    }

    /**
     * 初始化组件
     */
    async init() {
        try {
            console.log('正在初始化上传区域组件...');

            // 获取DOM元素
            this.uploadZone = document.getElementById('uploadZone');
            this.fileInput = document.getElementById('fileInput');

            if (!this.uploadZone || !this.fileInput) {
                throw new Error('未找到上传区域DOM元素');
            }

            // 绑定事件
            this.bindEvents();

            console.log('上传区域组件初始化完成');

        } catch (error) {
            console.error('上传区域组件初始化失败:', error);
            throw error;
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 点击上传区域
        this.uploadZone.addEventListener('click', () => {
            console.log('📁 点击上传区域，触发文件选择');
            this.fileInput.click();
        });

        // 拖拽事件
        this.uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.uploadZone.style.borderColor = 'var(--accent-color)';
        });

        this.uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            this.uploadZone.style.borderColor = 'var(--border-color)';
        });

        this.uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileUpload(files[0]);
            }
            this.uploadZone.style.borderColor = 'var(--border-color)';
        });

        // 文件选择事件
        this.fileInput.addEventListener('change', (e) => {
            console.log('📄 文件选择事件触发');
            if (e.target.files.length > 0) {
                this.handleFileUpload(e.target.files[0]);
            }
        });
    }

    /**
     * 处理文件上传
     * @param {File} file - 上传的文件
     */
    handleFileUpload(file) {
        console.log('📤 上传区域处理文件:', file.name);
        this.emit('fileUploaded', file);
    }

    /**
     * 更新主题
     * @param {string} theme - 主题名称
     */
    updateTheme(theme) {
        console.log('上传区域主题更新:', theme);
        // 主题更新逻辑可以在这里添加
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (!this.eventListeners.has(event)) return;

        const listeners = this.eventListeners.get(event);
        listeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('上传区域事件处理错误:', error);
            }
        });
    }
}
