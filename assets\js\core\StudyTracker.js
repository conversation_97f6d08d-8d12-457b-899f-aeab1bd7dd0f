/**
 * StudyTracker - 学习跟踪器主控制器
 * 负责协调各个功能模块，管理应用程序的核心业务逻辑
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { ThemeManager } from './ThemeManager.js';
import { FileHandler } from './FileHandler.js';
import { MarkdownParser } from './MarkdownParser.js';
import { ProgressManager } from './ProgressManager.js';
import { TimeTracker } from './TimeTracker.js';
import { StatsPanel } from '../components/StatsPanel.js';
import { UploadZone } from '../components/UploadZone.js';
import { ControlButtons } from '../components/ControlButtons.js';
import { StudyContent } from '../components/StudyContent.js';

/**
 * 学习跟踪器主类
 * 协调各个功能模块，管理应用程序状态
 */
export class StudyTracker {
    constructor() {
        // 核心数据
        this.studyData = [];
        this.isInitialized = false;

        // 功能模块
        this.themeManager = null;
        this.fileHandler = null;
        this.markdownParser = null;
        this.progressManager = null;
        this.timeTracker = null;

        // UI组件
        this.statsPanel = null;
        this.uploadZone = null;
        this.controlButtons = null;
        this.studyContent = null;
    }

    /**
     * 初始化StudyTracker
     */
    async init() {
        try {
            console.log('正在初始化StudyTracker...');

            // 初始化功能模块
            await this.initModules();

            // 初始化UI组件
            await this.initComponents();

            // 绑定事件
            this.bindEvents();

            // 启动时间跟踪
            this.timeTracker.start();

            this.isInitialized = true;
            console.log('StudyTracker初始化完成');

        } catch (error) {
            console.error('StudyTracker初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化功能模块
     */
    async initModules() {
        this.themeManager = new ThemeManager();
        this.fileHandler = new FileHandler();
        this.markdownParser = new MarkdownParser();
        this.progressManager = new ProgressManager();
        this.timeTracker = new TimeTracker();

        // 初始化各模块
        await this.themeManager.init();
        await this.fileHandler.init();
        await this.markdownParser.init();
        await this.progressManager.init();
        await this.timeTracker.init();
    }

    /**
     * 初始化UI组件
     */
    async initComponents() {
        this.statsPanel = new StatsPanel();
        this.uploadZone = new UploadZone();
        this.controlButtons = new ControlButtons();
        this.studyContent = new StudyContent();

        // 初始化各组件
        await this.statsPanel.init();
        await this.uploadZone.init();
        await this.controlButtons.init();
        await this.studyContent.init();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 主题切换事件
        this.themeManager.on('themeChanged', (theme) => {
            this.handleThemeChange(theme);
        });

        // 文件上传事件
        this.uploadZone.on('fileUploaded', (file) => {
            this.handleFileUpload(file);
        });

        // 控制按钮事件
        this.controlButtons.on('expandAll', () => this.expandAllModules());
        this.controlButtons.on('collapseAll', () => this.collapseAllModules());
        this.controlButtons.on('resetProgress', () => this.resetProgress());
        this.controlButtons.on('exportProgress', () => this.exportProgress());

        // 进度变更事件
        this.progressManager.on('progressChanged', (data) => {
            this.handleProgressChange(data);
        });

        // 时间跟踪事件
        this.timeTracker.on('timeUpdated', (timeData) => {
            this.handleTimeUpdate(timeData);
        });
    }

    /**
     * 处理主题变更
     * @param {string} theme - 新主题
     */
    handleThemeChange(theme) {
        console.log('主题已切换至:', theme);
        // 通知所有组件主题变更
        this.statsPanel.updateTheme(theme);
        this.uploadZone.updateTheme(theme);
        this.controlButtons.updateTheme(theme);
        this.studyContent.updateTheme(theme);
    }

    /**
     * 处理文件上传
     * @param {File} file - 上传的文件
     */
    async handleFileUpload(file) {
        try {
            console.log('处理文件上传:', file.name);
            
            // 读取文件内容
            const content = await this.fileHandler.readFile(file);
            
            // 解析Markdown
            this.studyData = await this.markdownParser.parse(content);
            
            // 渲染学习内容
            await this.studyContent.render(this.studyData);
            
            // 更新进度
            this.progressManager.updateData(this.studyData);
            
            // 开始学习跟踪
            this.timeTracker.startStudying();
            
            console.log('文件处理完成');
            
        } catch (error) {
            console.error('文件处理失败:', error);
            alert('文件处理失败：' + error.message);
        }
    }

    /**
     * 处理进度变更
     * @param {Object} progressData - 进度数据
     */
    handleProgressChange(progressData) {
        // 更新统计面板
        this.statsPanel.updateProgress(progressData);
        
        // 重新渲染学习内容
        this.studyContent.updateProgress(progressData);
    }

    /**
     * 处理时间更新
     * @param {Object} timeData - 时间数据
     */
    handleTimeUpdate(timeData) {
        // 更新统计面板
        this.statsPanel.updateTime(timeData);
    }

    /**
     * 展开所有模块
     */
    expandAllModules() {
        this.studyContent.expandAll();
    }

    /**
     * 折叠所有模块
     */
    collapseAllModules() {
        this.studyContent.collapseAll();
    }

    /**
     * 重置进度
     */
    resetProgress() {
        if (confirm('确定要重置所有学习进度吗？此操作不可恢复。')) {
            this.progressManager.reset();
        }
    }

    /**
     * 导出进度
     */
    exportProgress() {
        this.progressManager.export();
    }

    /**
     * 获取学习数据
     * @returns {Array} 学习数据
     */
    getStudyData() {
        return this.studyData;
    }

    /**
     * 检查是否已初始化
     * @returns {boolean}
     */
    isReady() {
        return this.isInitialized;
    }
}
