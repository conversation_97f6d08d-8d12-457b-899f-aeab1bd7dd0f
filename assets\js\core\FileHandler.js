/**
 * FileHandler - 文件处理器
 * 负责处理文件上传、读取和验证
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { SUPPORTED_FILE_TYPES, MAX_FILE_SIZE } from '../utils/constants.js';

/**
 * 文件处理器类
 * 处理文件上传、读取和验证功能
 */
export class FileHandler {
    constructor() {
        this.supportedTypes = SUPPORTED_FILE_TYPES;
        this.maxFileSize = MAX_FILE_SIZE;
    }

    /**
     * 初始化文件处理器
     */
    async init() {
        console.log('文件处理器初始化完成');
    }

    /**
     * 验证文件
     * @param {File} file - 要验证的文件
     * @returns {Object} 验证结果
     */
    validateFile(file) {
        const result = {
            valid: true,
            errors: []
        };

        // 检查文件是否存在
        if (!file) {
            result.valid = false;
            result.errors.push('未选择文件');
            return result;
        }

        // 检查文件类型
        const fileExtension = this.getFileExtension(file.name);
        if (!this.supportedTypes.includes(fileExtension)) {
            result.valid = false;
            result.errors.push(`不支持的文件类型: ${fileExtension}。支持的类型: ${this.supportedTypes.join(', ')}`);
        }

        // 检查文件大小
        if (file.size > this.maxFileSize) {
            result.valid = false;
            result.errors.push(`文件大小超过限制: ${this.formatFileSize(file.size)}。最大允许: ${this.formatFileSize(this.maxFileSize)}`);
        }

        // 检查文件名
        if (!this.isValidFileName(file.name)) {
            result.valid = false;
            result.errors.push('文件名包含无效字符');
        }

        return result;
    }

    /**
     * 读取文件内容
     * @param {File} file - 要读取的文件
     * @returns {Promise<string>} 文件内容
     */
    async readFile(file) {
        // 验证文件
        const validation = this.validateFile(file);
        if (!validation.valid) {
            throw new Error(validation.errors.join('; '));
        }

        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (event) => {
                try {
                    const content = event.target.result;
                    console.log(`文件读取成功: ${file.name} (${this.formatFileSize(file.size)})`);
                    resolve(content);
                } catch (error) {
                    reject(new Error('文件内容解析失败: ' + error.message));
                }
            };

            reader.onerror = () => {
                reject(new Error('文件读取失败: ' + reader.error?.message || '未知错误'));
            };

            reader.onabort = () => {
                reject(new Error('文件读取被中断'));
            };

            // 开始读取文件
            reader.readAsText(file, 'UTF-8');
        });
    }

    /**
     * 获取文件扩展名
     * @param {string} fileName - 文件名
     * @returns {string} 文件扩展名
     */
    getFileExtension(fileName) {
        const lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex === -1) return '';
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 验证文件名是否有效
     * @param {string} fileName - 文件名
     * @returns {boolean} 是否有效
     */
    isValidFileName(fileName) {
        // 检查文件名长度
        if (fileName.length === 0 || fileName.length > 255) {
            return false;
        }

        // 检查无效字符
        const invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
        if (invalidChars.test(fileName)) {
            return false;
        }

        // 检查保留名称（Windows）
        const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
        if (reservedNames.test(fileName)) {
            return false;
        }

        return true;
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';

        const units = ['B', 'KB', 'MB', 'GB'];
        const k = 1024;
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + units[i];
    }

    /**
     * 获取文件信息
     * @param {File} file - 文件对象
     * @returns {Object} 文件信息
     */
    getFileInfo(file) {
        return {
            name: file.name,
            size: file.size,
            type: file.type,
            extension: this.getFileExtension(file.name),
            lastModified: new Date(file.lastModified),
            formattedSize: this.formatFileSize(file.size)
        };
    }

    /**
     * 检查浏览器是否支持文件API
     * @returns {boolean} 是否支持
     */
    isFileAPISupported() {
        return !!(window.File && window.FileReader && window.FileList && window.Blob);
    }

    /**
     * 创建文件下载
     * @param {string} content - 文件内容
     * @param {string} fileName - 文件名
     * @param {string} mimeType - MIME类型
     */
    downloadFile(content, fileName, mimeType = 'text/plain') {
        try {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            link.style.display = 'none';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理URL对象
            setTimeout(() => URL.revokeObjectURL(url), 100);
            
            console.log(`文件下载已触发: ${fileName}`);
            
        } catch (error) {
            console.error('文件下载失败:', error);
            throw new Error('文件下载失败: ' + error.message);
        }
    }

    /**
     * 获取支持的文件类型
     * @returns {Array} 支持的文件类型列表
     */
    getSupportedTypes() {
        return [...this.supportedTypes];
    }

    /**
     * 获取最大文件大小
     * @returns {number} 最大文件大小（字节）
     */
    getMaxFileSize() {
        return this.maxFileSize;
    }
}
