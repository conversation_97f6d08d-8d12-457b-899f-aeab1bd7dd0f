/**
 * 主样式文件
 * 包含基础样式、布局和通用组件样式
 */

/* 卡片基础样式 */
.card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

/* 模块头部样式 */
.module-header {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.module-header:hover {
    background-color: var(--border-color);
}

/* 时间显示样式 */
.time-display {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

/* 文件图标样式 */
.file-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
}
