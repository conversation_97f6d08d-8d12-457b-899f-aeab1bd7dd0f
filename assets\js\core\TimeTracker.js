/**
 * TimeTracker - 时间跟踪器
 * 负责跟踪学习时间，包括今日学习时间和累计学习时间
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { storage } from '../utils/storage.js';

/**
 * 时间跟踪器类
 * 管理学习时间的记录和统计
 */
export class TimeTracker {
    constructor() {
        this.timeData = {
            today: 0,           // 今日学习时间（分钟）
            total: 0,           // 累计学习时间（分钟）
            lastDate: '',       // 最后记录日期
            sessions: []        // 学习会话记录
        };
        
        this.isStudying = false;
        this.startTime = null;
        this.timeInterval = null;
        this.eventListeners = new Map();
        this.storageKey = 'timeData';
        this.updateInterval = 60000; // 1分钟更新一次
    }

    /**
     * 初始化时间跟踪器
     */
    async init() {
        try {
            console.log('正在初始化时间跟踪器...');
            
            // 加载时间数据
            this.loadTimeData();
            
            // 检查是否是新的一天
            this.checkNewDay();
            
            // 绑定页面可见性变化事件
            this.bindVisibilityEvents();
            
            console.log('时间跟踪器初始化完成');
            
        } catch (error) {
            console.error('时间跟踪器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 从本地存储加载时间数据
     */
    loadTimeData() {
        try {
            const savedData = storage.get(this.storageKey);
            if (savedData) {
                this.timeData = { ...this.timeData, ...savedData };
            }
            console.log('时间数据加载完成:', this.timeData);
        } catch (error) {
            console.warn('时间数据加载失败，使用默认数据:', error);
        }
    }

    /**
     * 保存时间数据到本地存储
     */
    saveTimeData() {
        try {
            storage.set(this.storageKey, this.timeData);
        } catch (error) {
            console.error('时间数据保存失败:', error);
        }
    }

    /**
     * 检查是否是新的一天
     */
    checkNewDay() {
        const today = new Date().toDateString();
        if (this.timeData.lastDate !== today) {
            // 保存昨日数据到会话记录
            if (this.timeData.lastDate && this.timeData.today > 0) {
                this.timeData.sessions.push({
                    date: this.timeData.lastDate,
                    minutes: this.timeData.today
                });
            }
            
            // 重置今日时间
            this.timeData.today = 0;
            this.timeData.lastDate = today;
            this.saveTimeData();
            
            console.log('检测到新的一天，今日学习时间已重置');
        }
    }

    /**
     * 开始学习跟踪
     */
    start() {
        if (!this.timeInterval) {
            this.timeInterval = setInterval(() => {
                this.updateTime();
            }, this.updateInterval);
            
            console.log('时间跟踪已启动');
        }
    }

    /**
     * 停止学习跟踪
     */
    stop() {
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
            this.timeInterval = null;
            console.log('时间跟踪已停止');
        }
        
        this.pauseStudying();
    }

    /**
     * 开始学习会话
     */
    startStudying() {
        if (!this.isStudying && !document.hidden) {
            this.isStudying = true;
            this.startTime = Date.now();
            
            this.emit('studyStatusChanged', {
                isStudying: true,
                status: '学习中'
            });
            
            console.log('学习会话已开始');
        }
    }

    /**
     * 暂停学习会话
     */
    pauseStudying() {
        if (this.isStudying) {
            this.isStudying = false;
            
            // 计算本次会话时间
            if (this.startTime) {
                const sessionTime = Math.floor((Date.now() - this.startTime) / 60000); // 转换为分钟
                if (sessionTime > 0) {
                    this.addSessionTime(sessionTime);
                }
            }
            
            this.startTime = null;
            
            this.emit('studyStatusChanged', {
                isStudying: false,
                status: '已暂停'
            });
            
            console.log('学习会话已暂停');
        }
    }

    /**
     * 恢复学习会话
     */
    resumeStudying() {
        if (!this.isStudying && this.timeData.total > 0) {
            this.startStudying();
        }
    }

    /**
     * 添加会话时间
     * @param {number} minutes - 分钟数
     */
    addSessionTime(minutes) {
        this.timeData.today += minutes;
        this.timeData.total += minutes;
        this.saveTimeData();
        
        this.emit('timeUpdated', this.getTimeStats());
    }

    /**
     * 更新时间（定时调用）
     */
    updateTime() {
        if (this.isStudying && !document.hidden) {
            this.timeData.today += 1;
            this.timeData.total += 1;
            this.saveTimeData();
            
            this.emit('timeUpdated', this.getTimeStats());
        }
    }

    /**
     * 获取时间统计信息
     * @returns {Object} 时间统计
     */
    getTimeStats() {
        return {
            today: this.timeData.today,
            total: this.timeData.total,
            todayFormatted: this.formatTime(this.timeData.today),
            totalFormatted: this.formatTime(this.timeData.total),
            isStudying: this.isStudying,
            status: this.isStudying ? '学习中' : (this.timeData.total > 0 ? '已暂停' : '未开始'),
            averageDaily: this.getAverageDailyTime(),
            sessionsCount: this.timeData.sessions.length
        };
    }

    /**
     * 格式化时间显示
     * @param {number} minutes - 分钟数
     * @returns {string} 格式化后的时间
     */
    formatTime(minutes) {
        if (minutes < 60) {
            return `${minutes} 分钟`;
        } else if (minutes < 1440) { // 小于24小时
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            return mins > 0 ? `${hours} 小时 ${mins} 分钟` : `${hours} 小时`;
        } else {
            const days = Math.floor(minutes / 1440);
            const hours = Math.floor((minutes % 1440) / 60);
            const mins = minutes % 60;
            
            let result = `${days} 天`;
            if (hours > 0) result += ` ${hours} 小时`;
            if (mins > 0) result += ` ${mins} 分钟`;
            
            return result;
        }
    }

    /**
     * 获取平均每日学习时间
     * @returns {number} 平均分钟数
     */
    getAverageDailyTime() {
        if (this.timeData.sessions.length === 0) return 0;
        
        const totalSessionTime = this.timeData.sessions.reduce((sum, session) => sum + session.minutes, 0);
        const totalTime = totalSessionTime + this.timeData.today;
        const totalDays = this.timeData.sessions.length + (this.timeData.today > 0 ? 1 : 0);
        
        return Math.round(totalTime / totalDays);
    }

    /**
     * 获取学习历史记录
     * @returns {Array} 历史记录
     */
    getStudyHistory() {
        const history = [...this.timeData.sessions];
        
        // 添加今日记录（如果有学习时间）
        if (this.timeData.today > 0) {
            history.push({
                date: this.timeData.lastDate,
                minutes: this.timeData.today,
                isCurrent: true
            });
        }
        
        return history.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    /**
     * 重置时间数据
     */
    reset() {
        this.pauseStudying();
        
        this.timeData = {
            today: 0,
            total: 0,
            lastDate: new Date().toDateString(),
            sessions: []
        };
        
        this.saveTimeData();
        this.emit('timeUpdated', this.getTimeStats());
        
        console.log('时间数据已重置');
    }

    /**
     * 绑定页面可见性变化事件
     */
    bindVisibilityEvents() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseStudying();
            } else {
                this.resumeStudying();
            }
        });
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (!this.eventListeners.has(event)) return;

        const listeners = this.eventListeners.get(event);
        listeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('时间跟踪事件处理错误:', error);
            }
        });
    }

    /**
     * 销毁时间跟踪器
     */
    destroy() {
        this.stop();
        this.eventListeners.clear();
        console.log('时间跟踪器已销毁');
    }
}
