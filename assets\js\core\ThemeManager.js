/**
 * ThemeManager - 主题管理器
 * 负责管理应用程序的主题切换功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { storage } from '../utils/storage.js';
import { THEMES } from '../utils/constants.js';

/**
 * 主题管理器类
 * 处理主题切换、存储和应用
 */
export class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.themeToggleButton = null;
        this.eventListeners = new Map();
    }

    /**
     * 初始化主题管理器
     */
    async init() {
        try {
            console.log('正在初始化主题管理器...');

            // 从本地存储加载主题
            this.loadTheme();

            // 获取主题切换按钮
            this.themeToggleButton = document.getElementById('themeToggle');
            if (!this.themeToggleButton) {
                throw new Error('未找到主题切换按钮');
            }

            // 应用当前主题
            this.applyTheme(this.currentTheme);

            // 绑定事件
            this.bindEvents();

            console.log('主题管理器初始化完成，当前主题:', this.currentTheme);

        } catch (error) {
            console.error('主题管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 从本地存储加载主题
     */
    loadTheme() {
        const savedTheme = storage.get('theme');
        if (savedTheme && THEMES.includes(savedTheme)) {
            this.currentTheme = savedTheme;
        } else {
            this.currentTheme = 'light'; // 默认主题
        }
    }

    /**
     * 应用主题
     * @param {string} theme - 主题名称
     */
    applyTheme(theme) {
        if (!THEMES.includes(theme)) {
            console.warn('无效的主题:', theme);
            return;
        }

        // 设置HTML属性
        document.documentElement.setAttribute('data-theme', theme);

        // 更新主题切换按钮图标
        this.updateThemeIcon(theme);

        // 保存到本地存储
        storage.set('theme', theme);

        this.currentTheme = theme;
        console.log('主题已应用:', theme);
    }

    /**
     * 更新主题切换按钮图标
     * @param {string} theme - 当前主题
     */
    updateThemeIcon(theme) {
        if (!this.themeToggleButton) return;

        const icon = this.themeToggleButton.querySelector('i');
        if (icon) {
            icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    /**
     * 设置主题
     * @param {string} theme - 要设置的主题
     */
    setTheme(theme) {
        if (theme === this.currentTheme) return;

        this.applyTheme(theme);
        this.emit('themeChanged', theme);
    }

    /**
     * 获取当前主题
     * @returns {string} 当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 获取可用主题列表
     * @returns {Array} 主题列表
     */
    getAvailableThemes() {
        return [...THEMES];
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        if (this.themeToggleButton) {
            this.themeToggleButton.addEventListener('click', () => {
                this.toggleTheme();
            });
        }
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (!this.eventListeners.has(event)) return;

        const listeners = this.eventListeners.get(event);
        const index = listeners.indexOf(callback);
        if (index > -1) {
            listeners.splice(index, 1);
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (!this.eventListeners.has(event)) return;

        const listeners = this.eventListeners.get(event);
        listeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('主题事件处理错误:', error);
            }
        });
    }

    /**
     * 销毁主题管理器
     */
    destroy() {
        // 清理事件监听器
        this.eventListeners.clear();
        
        // 移除DOM事件
        if (this.themeToggleButton) {
            this.themeToggleButton.removeEventListener('click', this.toggleTheme);
        }

        console.log('主题管理器已销毁');
    }
}
