/**
 * ProgressManager - 进度管理器
 * 负责管理学习进度的跟踪、存储和统计
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { storage } from '../utils/storage.js';

/**
 * 进度管理器类
 * 管理学习进度数据和相关操作
 */
export class ProgressManager {
    constructor() {
        this.progress = {};
        this.studyData = [];
        this.eventListeners = new Map();
        this.storageKey = 'studyProgress';
    }

    /**
     * 初始化进度管理器
     */
    async init() {
        try {
            console.log('正在初始化进度管理器...');
            
            // 从本地存储加载进度数据
            this.loadProgress();
            
            console.log('进度管理器初始化完成');
            
        } catch (error) {
            console.error('进度管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 从本地存储加载进度数据
     */
    loadProgress() {
        try {
            const savedProgress = storage.get(this.storageKey);
            this.progress = savedProgress || {};
            console.log('进度数据加载完成，已完成项目数:', Object.keys(this.progress).length);
        } catch (error) {
            console.warn('进度数据加载失败，使用空数据:', error);
            this.progress = {};
        }
    }

    /**
     * 保存进度数据到本地存储
     */
    saveProgress() {
        try {
            storage.set(this.storageKey, this.progress);
            console.log('进度数据已保存');
        } catch (error) {
            console.error('进度数据保存失败:', error);
        }
    }

    /**
     * 更新学习数据
     * @param {Array} studyData - 学习数据
     */
    updateData(studyData) {
        this.studyData = studyData;
        this.emit('dataUpdated', this.getProgressStats());
    }

    /**
     * 切换文档完成状态
     * @param {string} docId - 文档ID
     * @param {boolean} completed - 是否完成
     */
    toggleDocumentCompletion(docId, completed) {
        if (completed) {
            this.progress[docId] = {
                completed: true,
                completedAt: new Date().toISOString()
            };
        } else {
            delete this.progress[docId];
        }

        this.saveProgress();
        this.emit('progressChanged', this.getProgressStats());
        
        console.log(`文档 ${docId} 状态已更新:`, completed ? '已完成' : '未完成');
    }

    /**
     * 检查文档是否已完成
     * @param {string} docId - 文档ID
     * @returns {boolean} 是否已完成
     */
    isDocumentCompleted(docId) {
        return !!(this.progress[docId] && this.progress[docId].completed);
    }

    /**
     * 获取文档完成时间
     * @param {string} docId - 文档ID
     * @returns {string|null} 完成时间
     */
    getDocumentCompletionTime(docId) {
        return this.progress[docId]?.completedAt || null;
    }

    /**
     * 获取进度统计信息
     * @returns {Object} 进度统计
     */
    getProgressStats() {
        const stats = {
            totalDocuments: 0,
            completedDocuments: 0,
            totalModules: this.studyData.length,
            completedModules: 0,
            totalResources: 0,
            completedResources: 0,
            progressPercentage: 0,
            moduleStats: []
        };

        this.studyData.forEach(module => {
            let moduleTotal = 0;
            let moduleCompleted = 0;
            const resourceStats = [];

            module.resources.forEach(resource => {
                stats.totalResources++;
                const resourceTotal = resource.documents.length;
                let resourceCompleted = 0;

                resource.documents.forEach(doc => {
                    stats.totalDocuments++;
                    moduleTotal++;
                    
                    if (this.isDocumentCompleted(doc.id)) {
                        stats.completedDocuments++;
                        moduleCompleted++;
                        resourceCompleted++;
                    }
                });

                if (resourceCompleted === resourceTotal && resourceTotal > 0) {
                    stats.completedResources++;
                }

                resourceStats.push({
                    id: resource.id,
                    title: resource.title,
                    total: resourceTotal,
                    completed: resourceCompleted,
                    percentage: resourceTotal > 0 ? Math.round((resourceCompleted / resourceTotal) * 100) : 0
                });
            });

            if (moduleCompleted === moduleTotal && moduleTotal > 0) {
                stats.completedModules++;
            }

            stats.moduleStats.push({
                id: module.id,
                title: module.title,
                total: moduleTotal,
                completed: moduleCompleted,
                percentage: moduleTotal > 0 ? Math.round((moduleCompleted / moduleTotal) * 100) : 0,
                resources: resourceStats
            });
        });

        stats.progressPercentage = stats.totalDocuments > 0 
            ? Math.round((stats.completedDocuments / stats.totalDocuments) * 100) 
            : 0;

        return stats;
    }

    /**
     * 重置所有进度
     */
    reset() {
        this.progress = {};
        this.saveProgress();
        this.emit('progressChanged', this.getProgressStats());
        console.log('所有进度已重置');
    }

    /**
     * 导出进度报告
     * @returns {Object} 进度报告
     */
    export() {
        const stats = this.getProgressStats();
        const report = {
            exportDate: new Date().toISOString(),
            summary: {
                totalDocuments: stats.totalDocuments,
                completedDocuments: stats.completedDocuments,
                progressPercentage: stats.progressPercentage,
                totalModules: stats.totalModules,
                completedModules: stats.completedModules,
                totalResources: stats.totalResources,
                completedResources: stats.completedResources
            },
            detailedProgress: stats.moduleStats,
            completionHistory: this.getCompletionHistory()
        };

        // 下载报告文件
        const fileName = `study_progress_${new Date().toISOString().split('T')[0]}.json`;
        const content = JSON.stringify(report, null, 2);
        
        try {
            const blob = new Blob([content], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            link.click();
            URL.revokeObjectURL(url);
            
            console.log('进度报告已导出:', fileName);
        } catch (error) {
            console.error('进度报告导出失败:', error);
        }

        return report;
    }

    /**
     * 获取完成历史记录
     * @returns {Array} 完成历史
     */
    getCompletionHistory() {
        const history = [];
        
        Object.entries(this.progress).forEach(([docId, data]) => {
            if (data.completed && data.completedAt) {
                // 查找文档信息
                const docInfo = this.findDocumentInfo(docId);
                history.push({
                    docId,
                    completedAt: data.completedAt,
                    ...docInfo
                });
            }
        });

        // 按完成时间排序
        return history.sort((a, b) => new Date(a.completedAt) - new Date(b.completedAt));
    }

    /**
     * 查找文档信息
     * @param {string} docId - 文档ID
     * @returns {Object} 文档信息
     */
    findDocumentInfo(docId) {
        for (const module of this.studyData) {
            for (const resource of module.resources) {
                const doc = resource.documents.find(d => d.id === docId);
                if (doc) {
                    return {
                        docTitle: doc.title,
                        resourceTitle: resource.title,
                        moduleTitle: module.title
                    };
                }
            }
        }
        return {
            docTitle: '未知文档',
            resourceTitle: '未知资源',
            moduleTitle: '未知模块'
        };
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (!this.eventListeners.has(event)) return;

        const listeners = this.eventListeners.get(event);
        listeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('进度事件处理错误:', error);
            }
        });
    }
}
