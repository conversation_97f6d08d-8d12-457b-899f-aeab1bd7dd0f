/**
 * 组件样式文件
 * 包含各种UI组件的专用样式
 */

/* 进度条样式 */
.progress-bar {
    background: linear-gradient(90deg, var(--success-color) 0%, var(--accent-color) 100%);
    transition: width 0.3s ease;
}

/* 自定义复选框样式 */
.checkbox-custom {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.checkbox-custom:checked {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.checkbox-custom:checked::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 12px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* 上传区域样式 */
.upload-zone {
    border: 2px dashed var(--border-color);
    transition: all 0.3s ease;
}

.upload-zone:hover {
    border-color: var(--accent-color);
    background-color: var(--bg-secondary);
}
