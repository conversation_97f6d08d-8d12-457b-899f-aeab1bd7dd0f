/**
 * ControlButtons - 控制按钮组件
 * 负责控制按钮功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

/**
 * 控制按钮组件类
 */
export class ControlButtons {
    constructor() {
        this.eventListeners = new Map();
        this.buttons = {};
    }

    /**
     * 初始化组件
     */
    async init() {
        try {
            console.log('正在初始化控制按钮组件...');

            // 获取按钮元素
            this.buttons = {
                expandAll: document.getElementById('expandAll'),
                collapseAll: document.getElementById('collapseAll'),
                resetProgress: document.getElementById('resetProgress'),
                exportProgress: document.getElementById('exportProgress')
            };

            // 检查按钮是否存在
            for (const [name, button] of Object.entries(this.buttons)) {
                if (!button) {
                    throw new Error(`未找到控制按钮: ${name}`);
                }
            }

            // 绑定事件
            this.bindEvents();

            console.log('控制按钮组件初始化完成');

        } catch (error) {
            console.error('控制按钮组件初始化失败:', error);
            throw error;
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 展开所有模块
        this.buttons.expandAll.addEventListener('click', () => {
            console.log('🔽 展开所有模块');
            this.emit('expandAll');
        });

        // 折叠所有模块
        this.buttons.collapseAll.addEventListener('click', () => {
            console.log('🔼 折叠所有模块');
            this.emit('collapseAll');
        });

        // 重置进度
        this.buttons.resetProgress.addEventListener('click', () => {
            console.log('🔄 重置进度');
            this.emit('resetProgress');
        });

        // 导出进度
        this.buttons.exportProgress.addEventListener('click', () => {
            console.log('💾 导出进度');
            this.emit('exportProgress');
        });
    }

    /**
     * 更新主题
     * @param {string} theme - 主题名称
     */
    updateTheme(theme) {
        console.log('控制按钮主题更新:', theme);
        // 主题更新逻辑可以在这里添加
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     */
    emit(event, data) {
        if (!this.eventListeners.has(event)) return;

        const listeners = this.eventListeners.get(event);
        listeners.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('控制按钮事件处理错误:', error);
            }
        });
    }
}
