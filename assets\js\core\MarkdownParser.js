/**
 * MarkdownParser - Markdown解析器
 * 负责解析Markdown文件内容，提取学习模块和资源信息
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

import { generateId } from '../utils/helpers.js';

/**
 * Markdown解析器类
 * 解析学习计划Markdown文件，生成结构化数据
 */
export class MarkdownParser {
    constructor() {
        this.parseOptions = {
            modulePrefix: '## ',
            resourcePrefix: '### ',
            documentPrefix: '- ',
            ignoreEmptyLines: true,
            trimWhitespace: true
        };
    }

    /**
     * 初始化解析器
     */
    async init() {
        console.log('Markdown解析器初始化完成');
    }

    /**
     * 解析Markdown内容
     * @param {string} content - Markdown内容
     * @returns {Array} 解析后的学习数据
     */
    async parse(content) {
        try {
            console.log('开始解析Markdown内容...');

            if (!content || typeof content !== 'string') {
                throw new Error('无效的Markdown内容');
            }

            const lines = this.preprocessContent(content);
            const studyData = this.parseLines(lines);

            console.log(`Markdown解析完成，共解析出 ${studyData.length} 个学习模块`);
            return studyData;

        } catch (error) {
            console.error('Markdown解析失败:', error);
            throw new Error('Markdown解析失败: ' + error.message);
        }
    }

    /**
     * 预处理内容
     * @param {string} content - 原始内容
     * @returns {Array} 处理后的行数组
     */
    preprocessContent(content) {
        let lines = content.split('\n');

        if (this.parseOptions.trimWhitespace) {
            lines = lines.map(line => line.trim());
        }

        if (this.parseOptions.ignoreEmptyLines) {
            lines = lines.filter(line => line.length > 0);
        }

        return lines;
    }

    /**
     * 解析行内容
     * @param {Array} lines - 行数组
     * @returns {Array} 解析后的数据结构
     */
    parseLines(lines) {
        const studyData = [];
        let currentModule = null;
        let currentResource = null;

        lines.forEach((line, index) => {
            try {
                if (this.isModuleLine(line)) {
                    currentModule = this.createModule(line);
                    studyData.push(currentModule);
                    currentResource = null;
                    
                } else if (this.isResourceLine(line)) {
                    if (currentModule) {
                        currentResource = this.createResource(line);
                        currentModule.resources.push(currentResource);
                    } else {
                        console.warn(`第 ${index + 1} 行：资源定义在模块外部，已忽略`);
                    }
                    
                } else if (this.isDocumentLine(line)) {
                    if (currentResource) {
                        const document = this.createDocument(line);
                        currentResource.documents.push(document);
                    } else {
                        console.warn(`第 ${index + 1} 行：文档定义在资源外部，已忽略`);
                    }
                }
            } catch (error) {
                console.warn(`第 ${index + 1} 行解析错误:`, error.message);
            }
        });

        return this.validateAndCleanData(studyData);
    }

    /**
     * 检查是否为模块行
     * @param {string} line - 行内容
     * @returns {boolean}
     */
    isModuleLine(line) {
        return line.startsWith(this.parseOptions.modulePrefix);
    }

    /**
     * 检查是否为资源行
     * @param {string} line - 行内容
     * @returns {boolean}
     */
    isResourceLine(line) {
        return line.startsWith(this.parseOptions.resourcePrefix);
    }

    /**
     * 检查是否为文档行
     * @param {string} line - 行内容
     * @returns {boolean}
     */
    isDocumentLine(line) {
        return line.startsWith(this.parseOptions.documentPrefix);
    }

    /**
     * 创建模块对象
     * @param {string} line - 模块行内容
     * @returns {Object} 模块对象
     */
    createModule(line) {
        const title = line.substring(this.parseOptions.modulePrefix.length).trim();
        
        if (!title) {
            throw new Error('模块标题不能为空');
        }

        return {
            id: generateId('module'),
            title: title,
            type: 'module',
            resources: [],
            expanded: false,
            createdAt: new Date().toISOString()
        };
    }

    /**
     * 创建资源对象
     * @param {string} line - 资源行内容
     * @returns {Object} 资源对象
     */
    createResource(line) {
        const title = line.substring(this.parseOptions.resourcePrefix.length).trim();
        
        if (!title) {
            throw new Error('资源标题不能为空');
        }

        return {
            id: generateId('resource'),
            title: title,
            type: 'resource',
            documents: [],
            expanded: false,
            createdAt: new Date().toISOString()
        };
    }

    /**
     * 创建文档对象
     * @param {string} line - 文档行内容
     * @returns {Object} 文档对象
     */
    createDocument(line) {
        const title = line.substring(this.parseOptions.documentPrefix.length).trim();
        
        if (!title) {
            throw new Error('文档标题不能为空');
        }

        return {
            id: generateId('doc'),
            title: title,
            type: 'document',
            completed: false,
            createdAt: new Date().toISOString()
        };
    }

    /**
     * 验证和清理数据
     * @param {Array} studyData - 原始数据
     * @returns {Array} 清理后的数据
     */
    validateAndCleanData(studyData) {
        return studyData.filter(module => {
            // 移除没有资源的模块
            if (module.resources.length === 0) {
                console.warn(`模块 "${module.title}" 没有资源，已移除`);
                return false;
            }

            // 清理没有文档的资源
            module.resources = module.resources.filter(resource => {
                if (resource.documents.length === 0) {
                    console.warn(`资源 "${resource.title}" 没有文档，已移除`);
                    return false;
                }
                return true;
            });

            return module.resources.length > 0;
        });
    }

    /**
     * 获取解析统计信息
     * @param {Array} studyData - 学习数据
     * @returns {Object} 统计信息
     */
    getParseStats(studyData) {
        let totalModules = studyData.length;
        let totalResources = 0;
        let totalDocuments = 0;

        studyData.forEach(module => {
            totalResources += module.resources.length;
            module.resources.forEach(resource => {
                totalDocuments += resource.documents.length;
            });
        });

        return {
            modules: totalModules,
            resources: totalResources,
            documents: totalDocuments,
            avgResourcesPerModule: totalModules > 0 ? (totalResources / totalModules).toFixed(2) : 0,
            avgDocumentsPerResource: totalResources > 0 ? (totalDocuments / totalResources).toFixed(2) : 0
        };
    }

    /**
     * 设置解析选项
     * @param {Object} options - 解析选项
     */
    setParseOptions(options) {
        this.parseOptions = { ...this.parseOptions, ...options };
    }

    /**
     * 获取解析选项
     * @returns {Object} 当前解析选项
     */
    getParseOptions() {
        return { ...this.parseOptions };
    }
}
