/**
 * 主题样式文件
 * 包含CSS变量定义和主题切换相关样式
 * 支持浅色和深色主题
 */

/* 浅色主题（默认） */
:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --border-color: #e2e8f0;
    --accent-color: #3182ce;
    --success-color: #38a169;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 深色主题 */
[data-theme="dark"] {
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --text-primary: #f7fafc;
    --text-secondary: #cbd5e0;
    --border-color: #4a5568;
    --accent-color: #63b3ed;
    --success-color: #68d391;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

/* 基础主题应用 */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}
