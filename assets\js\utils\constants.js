/**
 * Constants - 常量定义
 * 定义应用程序中使用的各种常量
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

// 应用程序信息
export const APP_INFO = {
    name: '学习路线可视化系统',
    version: '1.0.0',
    author: 'AI Assistant',
    description: '一个用于管理和跟踪学习进度的可视化系统'
};

// 主题相关常量
export const THEMES = ['light', 'dark'];

export const THEME_CONFIG = {
    light: {
        name: '浅色主题',
        icon: 'fas fa-sun'
    },
    dark: {
        name: '深色主题',
        icon: 'fas fa-moon'
    }
};

// 文件处理相关常量
export const SUPPORTED_FILE_TYPES = ['md'];

export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

export const FILE_TYPE_CONFIG = {
    md: {
        name: 'Markdown文件',
        mimeTypes: ['text/markdown', 'text/x-markdown'],
        description: '支持标准Markdown格式的学习计划文件'
    }
};

// 本地存储键名
export const STORAGE_KEYS = {
    THEME: 'theme',
    PROGRESS: 'studyProgress',
    TIME_DATA: 'timeData',
    USER_PREFERENCES: 'userPreferences',
    LAST_BACKUP: 'lastBackup'
};

// 时间相关常量
export const TIME_CONFIG = {
    UPDATE_INTERVAL: 60000,        // 时间更新间隔（毫秒）
    SESSION_TIMEOUT: 1800000,      // 会话超时时间（30分钟）
    BACKUP_INTERVAL: 86400000,     // 备份间隔（24小时）
    TIME_FORMAT: 'YYYY-MM-DD HH:mm:ss'
};

// UI相关常量
export const UI_CONFIG = {
    ANIMATION_DURATION: 300,       // 动画持续时间（毫秒）
    DEBOUNCE_DELAY: 300,          // 防抖延迟（毫秒）
    THROTTLE_LIMIT: 100,          // 节流限制（毫秒）
    TOAST_DURATION: 3000,         // 提示消息持续时间（毫秒）
    MODAL_Z_INDEX: 9999           // 模态框层级
};

// 进度相关常量
export const PROGRESS_CONFIG = {
    AUTO_SAVE: true,              // 自动保存进度
    SAVE_INTERVAL: 5000,          // 保存间隔（毫秒）
    BACKUP_COUNT: 10,             // 保留备份数量
    EXPORT_FORMAT: 'json'         // 导出格式
};

// 事件名称常量
export const EVENTS = {
    // 主题事件
    THEME_CHANGED: 'themeChanged',
    
    // 文件事件
    FILE_UPLOADED: 'fileUploaded',
    FILE_PROCESSED: 'fileProcessed',
    FILE_ERROR: 'fileError',
    
    // 进度事件
    PROGRESS_CHANGED: 'progressChanged',
    PROGRESS_RESET: 'progressReset',
    PROGRESS_EXPORTED: 'progressExported',
    
    // 时间事件
    TIME_UPDATED: 'timeUpdated',
    STUDY_STARTED: 'studyStarted',
    STUDY_PAUSED: 'studyPaused',
    STUDY_RESUMED: 'studyResumed',
    
    // UI事件
    MODULE_EXPANDED: 'moduleExpanded',
    MODULE_COLLAPSED: 'moduleCollapsed',
    DOCUMENT_COMPLETED: 'documentCompleted',
    
    // 系统事件
    APP_INITIALIZED: 'appInitialized',
    APP_ERROR: 'appError',
    DATA_LOADED: 'dataLoaded',
    DATA_SAVED: 'dataSaved'
};

// 错误消息常量
export const ERROR_MESSAGES = {
    FILE_NOT_SUPPORTED: '不支持的文件类型',
    FILE_TOO_LARGE: '文件大小超过限制',
    FILE_READ_ERROR: '文件读取失败',
    PARSE_ERROR: 'Markdown解析失败',
    STORAGE_ERROR: '数据存储失败',
    NETWORK_ERROR: '网络连接失败',
    UNKNOWN_ERROR: '未知错误'
};

// 成功消息常量
export const SUCCESS_MESSAGES = {
    FILE_UPLOADED: '文件上传成功',
    PROGRESS_SAVED: '进度保存成功',
    PROGRESS_RESET: '进度重置成功',
    PROGRESS_EXPORTED: '进度导出成功',
    THEME_CHANGED: '主题切换成功',
    DATA_BACKUP: '数据备份成功'
};

// 默认配置
export const DEFAULT_CONFIG = {
    theme: 'light',
    autoSave: true,
    showNotifications: true,
    soundEnabled: false,
    language: 'zh-CN',
    timeFormat: '24h',
    dateFormat: 'YYYY-MM-DD'
};

// API相关常量（如果需要）
export const API_CONFIG = {
    BASE_URL: '',
    TIMEOUT: 10000,
    RETRY_COUNT: 3,
    RETRY_DELAY: 1000
};

// 正则表达式常量
export const REGEX_PATTERNS = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    URL: /^https?:\/\/.+/,
    MARKDOWN_HEADING: /^#{1,6}\s+(.+)$/,
    MARKDOWN_LIST: /^[\s]*[-*+]\s+(.+)$/,
    FILE_EXTENSION: /\.([a-zA-Z0-9]+)$/
};

// 键盘快捷键常量
export const KEYBOARD_SHORTCUTS = {
    TOGGLE_THEME: 'KeyT',
    EXPAND_ALL: 'KeyE',
    COLLAPSE_ALL: 'KeyC',
    RESET_PROGRESS: 'KeyR',
    EXPORT_PROGRESS: 'KeyS',
    UPLOAD_FILE: 'KeyU'
};

// 响应式断点常量
export const BREAKPOINTS = {
    MOBILE: 768,
    TABLET: 1024,
    DESKTOP: 1200,
    LARGE: 1440
};

// 颜色常量
export const COLORS = {
    PRIMARY: '#3182ce',
    SUCCESS: '#38a169',
    WARNING: '#d69e2e',
    ERROR: '#e53e3e',
    INFO: '#3182ce',
    GRAY: '#718096'
};

// 图标常量
export const ICONS = {
    MODULE: 'fas fa-book',
    RESOURCE: 'fas fa-folder',
    DOCUMENT: 'fas fa-file',
    COMPLETED: 'fas fa-check-circle',
    PENDING: 'fas fa-circle',
    EXPAND: 'fas fa-chevron-down',
    COLLAPSE: 'fas fa-chevron-right',
    UPLOAD: 'fas fa-cloud-upload-alt',
    DOWNLOAD: 'fas fa-download',
    RESET: 'fas fa-undo',
    SETTINGS: 'fas fa-cog',
    THEME_LIGHT: 'fas fa-sun',
    THEME_DARK: 'fas fa-moon'
};

// 动画类名常量
export const ANIMATION_CLASSES = {
    FADE_IN: 'fade-in',
    FADE_OUT: 'fade-out',
    SLIDE_IN: 'slide-in',
    SLIDE_OUT: 'slide-out',
    BOUNCE: 'bounce',
    SHAKE: 'shake'
};

// 状态常量
export const STATUS = {
    IDLE: 'idle',
    LOADING: 'loading',
    SUCCESS: 'success',
    ERROR: 'error',
    PENDING: 'pending',
    COMPLETED: 'completed'
};
